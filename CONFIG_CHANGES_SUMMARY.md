# 配置修改总结

## 概述

根据您的要求，我已经对三个项目（bert、transformer、transformer2）的配置进行了统一规范化，主要目标是：

1. 统一目录配置管理
2. 消除`output_dir`这个可读性差的变量名
3. 将所有目录路径集中在config.py中定义
4. 支持自动目录创建
5. 配置HuggingFace缓存目录和离线模式

## 主要修改内容

### 1. BERT项目 (`bert/`)

#### 配置文件修改 (`bert/config.py`)
- **移除**: `output_dir` 变量
- **新增**: 
  - `model_save_dir`: `/Users/<USER>/work/python/deepai/saved_model/bert`
  - `log_dir`: `/Users/<USER>/work/python/deepai/logs/bert`
  - `cache_dir`: `/Users/<USER>/.cache/huggingface/datasets`
- **新增函数**: `create_directories()` - 自动创建所有必要目录
- **更新**: `setup_logging()` - 使用新的日志目录配置

#### 训练器修改 (`bert/trainer.py`)
- 将所有 `self.output_dir` 替换为 `self.model_save_dir`
- 更新模型保存、检查点保存等路径引用
- 修复了pydantic的`dict()`方法弃用警告，改为`model_dump()`

#### 数据加载器修改 (`bert/data_loader.py`)
- 为所有数据集加载函数添加`cache_dir`参数
- 为tokenizer加载添加`cache_dir`参数

### 2. Transformer项目 (`transformer/`)

#### 配置文件修改 (`transformer/config.py`)
- **重命名**: `model_save_path` → `model_save_dir`
- **重命名**: `vocab_save_path` → `vocab_save_dir`
- **更新路径**:
  - `model_save_dir`: `/Users/<USER>/work/python/deepai/saved_model/transformer`
  - `vocab_save_dir`: `/Users/<USER>/work/python/deepai/saved_model/transformer/vocab`
  - `log_dir`: `/Users/<USER>/work/python/deepai/logs/transformer`
  - `cache_dir`: `/Users/<USER>/.cache/huggingface/datasets`
- **新增函数**: `create_directories()` - 自动创建所有必要目录

#### 训练器修改 (`transformer/trainer.py`)
- 更新所有路径引用，使用新的目录配置变量

#### 数据加载器修改 (`transformer/data_loader.py`)
- 更新缓存目录配置
- 修复词汇表保存路径引用

### 3. Transformer2项目 (`transformer2/`)

#### 配置文件修改 (`transformer2/config.py`)
- **移除**: `output_dir` 变量
- **新增**:
  - `model_save_dir`: `/Users/<USER>/work/python/deepai/saved_model/transformer2`
  - `log_dir`: `/Users/<USER>/work/python/deepai/logs/transformer2`
  - `vocab_save_dir`: `/Users/<USER>/work/python/deepai/saved_model/transformer2/vocab`
- **更新**: `cache_dir` 路径
- **新增函数**: `create_directories()` - 自动创建所有必要目录
- **更新**: `setup_logging()` - 使用新的日志目录配置

#### 训练器修改 (`transformer2/trainer.py`)
- 将 `self.output_dir` 替换为 `self.model_save_dir`
- 更新所有模型保存路径引用

#### 数据加载器修改 (`transformer2/data_loader.py`)
- 已经使用正确的缓存目录配置

## 目录结构

修改后的目录结构如下：

```
/Users/<USER>/work/python/deepai/
├── saved_model/
│   ├── bert/                    # BERT模型保存目录
│   ├── transformer/             # Transformer模型保存目录
│   │   └── vocab/              # Transformer词汇表目录
│   └── transformer2/           # Transformer2模型保存目录
│       └── vocab/              # Transformer2词汇表目录
├── logs/
│   ├── bert/                   # BERT日志目录
│   ├── transformer/            # Transformer日志目录
│   └── transformer2/           # Transformer2日志目录
└── /Users/<USER>/.cache/huggingface/datasets/  # HuggingFace缓存目录
```

## 新增功能

### 1. 自动目录创建
每个项目的config.py都新增了`create_directories()`函数，可以自动创建所有必要的目录：

```python
# 使用示例
from config import create_directories
create_directories()  # 自动创建所有目录
```

### 2. 统一的缓存配置
所有项目都配置了统一的HuggingFace缓存目录，支持：
- 数据集缓存
- 模型缓存
- Tokenizer缓存

### 3. 改进的日志系统
- 日志文件自动按时间戳命名
- 同时输出到控制台和文件
- 第三方库日志级别控制

## 使用方式

### 1. 直接从全局配置获取路径
```python
from config import TRAINING_CONFIG

# 获取模型保存目录
model_dir = TRAINING_CONFIG.model_save_dir

# 获取日志目录
log_dir = TRAINING_CONFIG.log_dir

# 获取缓存目录
cache_dir = TRAINING_CONFIG.cache_dir
```

### 2. 自动创建目录
```python
from config import create_directories
create_directories()  # 一键创建所有必要目录
```

### 3. 设置日志系统
```python
from config import setup_logging
logger = setup_logging()  # 自动配置日志系统
```

## 验证

运行测试脚本验证所有修改：

```bash
python simple_test.py
```

测试结果显示所有配置都正确，目录创建成功。

## 总结

✅ **完成的修改**：
1. 移除了可读性差的`output_dir`变量
2. 统一了所有目录配置，集中在config.py中管理
3. 实现了自动目录创建功能
4. 配置了统一的HuggingFace缓存目录
5. 改进了日志系统
6. 更新了所有相关代码引用

✅ **目录结构**：
- 模型保存：`/Users/<USER>/work/python/deepai/saved_model/{项目名}/`
- 日志保存：`/Users/<USER>/work/python/deepai/logs/{项目名}/`
- 数据缓存：`/Users/<USER>/.cache/huggingface/datasets/`

✅ **使用方式**：
- 所有路径都从全局配置获取，无需手动传参
- 支持一键创建所有必要目录
- 自动配置日志系统

所有修改都已经过测试验证，可以正常使用。
